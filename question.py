import requests
from tqdm import tqdm
import json
import time

API_URL = "http://localhost:8000/generate"

test_questions = [
      {
        "type": "error_analysis",
        "prompt": """### Instruction
Analyze the following Java code for critical concurrency bugs, memory visibility issues, and JMM violations. Identify all race conditions, deadlock potential, and explain the specific consequences of each issue:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class HighFrequencyTrader {
    private volatile boolean isActive = false;
    private long totalVolume = 0;
    private final AtomicReference<MarketData> latestData = new AtomicReference<>();
    private final ReentrantReadWriteLock dataLock = new ReentrantReadWriteLock();
    private final ConcurrentHashMap<String, Position> positions = new ConcurrentHashMap<>();
    private static final Object STATIC_LOCK = new Object();

    public void startTrading() {
        synchronized (STATIC_LOCK) {
            if (!isActive) {
                isActive = true;
                new Thread(this::processMarketData).start();
                new Thread(this::executeOrders).start();
            }
        }
    }

    private void processMarketData() {
        while (isActive) {
            dataLock.writeLock().lock();
            try {
                MarketData newData = fetchMarketData();
                latestData.set(newData);
                totalVolume += newData.getVolume();

                synchronized (positions) {
                    updatePositions(newData);
                }
            } finally {
                dataLock.writeLock().unlock();
            }
        }
    }

    private void executeOrders() {
        while (isActive) {
            synchronized (positions) {
                dataLock.readLock().lock();
                try {
                    MarketData data = latestData.get();
                    if (data != null && shouldExecute(data)) {
                        synchronized (STATIC_LOCK) {
                            executeOrder(data);
                            totalVolume -= data.getVolume();
                        }
                    }
                } finally {
                    dataLock.readLock().unlock();
                }
            }
        }
    }

    public long getTotalVolume() {
        return totalVolume;
    }
}
```

### Response
        """
    },
]

test_questions1 = [
    {
        "type": "error_analysis",
        "prompt": """### Instruction
Analyze the following Java code for critical concurrency bugs, memory visibility issues, and JMM violations. Identify all race conditions, deadlock potential, and explain the specific consequences of each issue:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class HighFrequencyTrader {
    private volatile boolean isActive = false;
    private long totalVolume = 0;
    private final AtomicReference<MarketData> latestData = new AtomicReference<>();
    private final ReentrantReadWriteLock dataLock = new ReentrantReadWriteLock();
    private final ConcurrentHashMap<String, Position> positions = new ConcurrentHashMap<>();
    private static final Object STATIC_LOCK = new Object();

    public void startTrading() {
        synchronized (STATIC_LOCK) {
            if (!isActive) {
                isActive = true;
                new Thread(this::processMarketData).start();
                new Thread(this::executeOrders).start();
            }
        }
    }

    private void processMarketData() {
        while (isActive) {
            dataLock.writeLock().lock();
            try {
                MarketData newData = fetchMarketData();
                latestData.set(newData);
                totalVolume += newData.getVolume();

                synchronized (positions) {
                    updatePositions(newData);
                }
            } finally {
                dataLock.writeLock().unlock();
            }
        }
    }

    private void executeOrders() {
        while (isActive) {
            synchronized (positions) {
                dataLock.readLock().lock();
                try {
                    MarketData data = latestData.get();
                    if (data != null && shouldExecute(data)) {
                        synchronized (STATIC_LOCK) {
                            executeOrder(data);
                            totalVolume -= data.getVolume();
                        }
                    }
                } finally {
                    dataLock.readLock().unlock();
                }
            }
        }
    }

    public long getTotalVolume() {
        return totalVolume;
    }
}
```

### Response
        """
    },
    {
        "type": "error_analysis",
        "prompt": """### Instruction
Identify all type safety violations, heap pollution risks, and generic type system exploits in this advanced container implementation. Explain how type erasure creates security vulnerabilities:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
@SuppressWarnings("unchecked")
public class TypeUnsafeContainer<T extends Serializable & Comparable<? super T>> {
    private final Map<Class<?>, Object[]> storage = new HashMap<>();
    private final Class<T> typeClass;

    public TypeUnsafeContainer(Class<T> typeClass) {
        this.typeClass = typeClass;
    }

    public <U extends T> void store(Class<U> clazz, U... items) {
        Object[] existing = storage.get(clazz);
        if (existing == null) {
            storage.put(clazz, items.clone());
        } else {
            Object[] combined = Arrays.copyOf(existing, existing.length + items.length);
            System.arraycopy(items, 0, combined, existing.length, items.length);
            storage.put(clazz, combined);
        }
    }

    public <U> U[] retrieve(Class<U> clazz) {
        Object[] items = storage.get(clazz);
        if (items == null) return null;

        U[] result = (U[]) Array.newInstance(clazz, items.length);
        for (int i = 0; i < items.length; i++) {
            result[i] = (U) items[i];
        }
        return result;
    }

    public <U, V extends U> List<V> getFilteredItems(Class<U> baseClass, Predicate<U> filter) {
        Object[] items = storage.get(baseClass);
        if (items == null) return Collections.emptyList();

        return Arrays.stream(items)
                    .map(item -> (U) item)
                    .filter(filter)
                    .map(item -> (V) item)
                    .collect(Collectors.toList());
    }

    public void mergeFrom(TypeUnsafeContainer<?> other) {
        other.storage.forEach((clazz, items) -> {
            Object[] ourItems = storage.get(clazz);
            if (ourItems == null) {
                storage.put(clazz, items.clone());
            } else {
                Object[] merged = Arrays.copyOf(ourItems, ourItems.length + items.length);
                System.arraycopy(items, 0, merged, ourItems.length, items.length);
                storage.put(clazz, merged);
            }
        });
    }

    public <U> U unsafeGet(int index) {
        return (U) storage.values().iterator().next()[index];
    }
}
```

### Response
        """
    },
    {
        "type": "error_analysis",
        "prompt": """### Instruction
Analyze this reflection-based dependency injection framework for critical security vulnerabilities, arbitrary code execution risks, and privilege escalation exploits:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class UnsafeDIContainer {
    private static final Unsafe unsafe = getUnsafe();
    private final Map<String, Object> singletons = new ConcurrentHashMap<>();
    private final Map<Class<?>, Constructor<?>> constructorCache = new WeakHashMap<>();

    @SuppressWarnings("unchecked")
    public <T> T getInstance(String className) throws Exception {
        Object instance = singletons.get(className);
        if (instance != null) return (T) instance;

        Class<?> clazz = Class.forName(className);

        // Bypass constructor access control
        Constructor<?> constructor = constructorCache.computeIfAbsent(clazz, k -> {
            try {
                Constructor<?>[] constructors = k.getDeclaredConstructors();
                Constructor<?> target = constructors[0];
                target.setAccessible(true);
                return target;
            } catch (Exception e) {
                return null;
            }
        });

        T newInstance;
        if (constructor == null) {
            // Use Unsafe to bypass all constructors
            newInstance = (T) unsafe.allocateInstance(clazz);
        } else {
            Object[] args = resolveConstructorArgs(constructor);
            newInstance = (T) constructor.newInstance(args);
        }

        injectDependencies(newInstance);

        if (clazz.isAnnotationPresent(Singleton.class)) {
            singletons.put(className, newInstance);
        }

        return newInstance;
    }

    private Object[] resolveConstructorArgs(Constructor<?> constructor) throws Exception {
        Parameter[] params = constructor.getParameters();
        Object[] args = new Object[params.length];

        for (int i = 0; i < params.length; i++) {
            Parameter param = params[i];
            Inject inject = param.getAnnotation(Inject.class);

            if (inject != null && !inject.value().isEmpty()) {
                args[i] = getInstance(inject.value());
            } else {
                // Auto-resolve by type name
                args[i] = getInstance(param.getType().getName());
            }
        }
        return args;
    }

    private void injectDependencies(Object instance) throws Exception {
        Class<?> clazz = instance.getClass();

        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                Inject inject = field.getAnnotation(Inject.class);
                if (inject != null) {
                    field.setAccessible(true);

                    String targetClass = inject.value().isEmpty() ?
                        field.getType().getName() : inject.value();

                    Object dependency = getInstance(targetClass);

                    // Use Unsafe for final field injection
                    if (Modifier.isFinal(field.getModifiers())) {
                        long fieldOffset = unsafe.objectFieldOffset(field);
                        unsafe.putObject(instance, fieldOffset, dependency);
                    } else {
                        field.set(instance, dependency);
                    }
                }
            }
            clazz = clazz.getSuperclass();
        }
    }

    public void executeMethod(String className, String methodName, Object... args) throws Exception {
        Object instance = getInstance(className);
        Class<?> clazz = instance.getClass();

        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                method.setAccessible(true);
                method.invoke(instance, args);
                break;
            }
        }
    }

    private static Unsafe getUnsafe() {
        try {
            Field field = Unsafe.class.getDeclaredField("theUnsafe");
            field.setAccessible(true);
            return (Unsafe) field.get(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

@Retention(RetentionPolicy.RUNTIME)
@interface Inject { String value() default ""; }

@Retention(RetentionPolicy.RUNTIME)
@interface Singleton {}
```

### Response
        """
    },
    {
        "type": "error_analysis",
        "prompt": """### Instruction
Identify security vulnerabilities, sandbox escape potential, and bytecode injection risks in this dynamic class loading system:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class DynamicClassSystem extends ClassLoader {
    private final Map<String, byte[]> classBytes = new ConcurrentHashMap<>();
    private final Map<String, Class<?>> loadedClasses = new WeakHashMap<>();
    private static final MethodHandles.Lookup lookup = MethodHandles.lookup();

    public Class<?> loadFromBytes(String className, byte[] bytecode) {
        classBytes.put(className, bytecode);

        try {
            Class<?> clazz = loadedClasses.get(className);
            if (clazz == null) {
                clazz = defineClass(className, bytecode, 0, bytecode.length);
                loadedClasses.put(className, clazz);
            }
            return clazz;
        } catch (Exception e) {
            throw new RuntimeException("Failed to load class: " + className, e);
        }
    }

    public Object createInstanceWithPrivileges(String className, Object... args) throws Exception {
        Class<?> clazz = loadClass(className);

        // Bypass security manager
        Constructor<?>[] constructors = clazz.getDeclaredConstructors();
        Constructor<?> constructor = constructors[0];

        return AccessController.doPrivileged(new PrivilegedExceptionAction<Object>() {
            @Override
            public Object run() throws Exception {
                constructor.setAccessible(true);
                return constructor.newInstance(args);
            }
        });
    }

    public void injectMethod(String targetClass, String methodName, String code) throws Exception {
        // Simplified bytecode manipulation (extremely dangerous)
        byte[] originalBytes = classBytes.get(targetClass);
        if (originalBytes == null) {
            throw new IllegalStateException("Class not found: " + targetClass);
        }

        // This would normally use ASM or similar, but showing concept
        String javaCode = generateMethodCode(methodName, code);
        byte[] modifiedBytes = compileAndInject(originalBytes, javaCode);

        // Redefine class at runtime
        Instrumentation instrumentation = getInstrumentation();
        if (instrumentation != null) {
            ClassDefinition definition = new ClassDefinition(loadClass(targetClass), modifiedBytes);
            instrumentation.redefineClasses(definition);
        }
    }

    public Object invokeHiddenMethod(Object instance, String methodName, Object... args) throws Exception {
        Class<?> clazz = instance.getClass();

        // Use MethodHandle to bypass access control
        MethodHandle handle = null;
        for (Method method : clazz.getDeclaredMethods()) {
            if (method.getName().equals(methodName)) {
                MethodHandles.Lookup privateLookup = MethodHandles.privateLookupIn(clazz, lookup);
                handle = privateLookup.unreflect(method);
                break;
            }
        }

        if (handle != null) {
            return handle.invokeWithArguments(instance, args);
        }

        throw new NoSuchMethodException(methodName);
    }

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        byte[] bytes = classBytes.get(name);
        if (bytes != null) {
            return defineClass(name, bytes, 0, bytes.length);
        }
        return super.findClass(name);
    }

    private String generateMethodCode(String methodName, String code) {
        return String.format(
            "public Object %s() { %s }",
            methodName, code
        );
    }

    private byte[] compileAndInject(byte[] originalBytes, String methodCode) {
        // Extremely simplified - real implementation would be much more complex
        // This represents the dangerous concept of runtime code injection
        return originalBytes; // Placeholder
    }

    private static Instrumentation getInstrumentation() {
        // Dangerous: accessing JVM internals
        try {
            Class<?> vmClass = Class.forName("sun.instrument.InstrumentationImpl");
            Field field = vmClass.getDeclaredField("INSTANCE");
            field.setAccessible(true);
            return (Instrumentation) field.get(null);
        } catch (Exception e) {
            return null;
        }
    }
}
```

### Response
        """
    },
    {
        "type": "error_analysis",
        "prompt": """### Instruction
Analyze this memory-intensive caching system for memory leaks, GC pressure issues, and OutOfMemoryError scenarios. Identify the specific JVM memory model violations:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class MemoryIntensiveCache<K, V> {
    private final Map<K, SoftReference<CacheEntry<V>>> cache = new ConcurrentHashMap<>();
    private final Map<K, WeakReference<V>> backupCache = new WeakHashMap<>();
    private final ReferenceQueue<CacheEntry<V>> referenceQueue = new ReferenceQueue<>();
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    private final List<V> pinnedObjects = Collections.synchronizedList(new ArrayList<>());

    // Potential memory bomb
    private static final Map<Object, Object> GLOBAL_CACHE = new ConcurrentHashMap<>();
    private final ThreadLocal<Map<K, V>> threadLocalCache = new ThreadLocal<Map<K, V>>() {
        @Override
        protected Map<K, V> initialValue() {
            return new HashMap<>();
        }
    };

    public MemoryIntensiveCache() {
        cleanupExecutor.scheduleAtFixedRate(this::cleanup, 0, 1, TimeUnit.MINUTES);
    }

    public void put(K key, V value) {
        // Multiple references to same object - memory leak potential
        CacheEntry<V> entry = new CacheEntry<>(value, System.currentTimeMillis());

        cache.put(key, new SoftReference<>(entry, referenceQueue));
        backupCache.put(key, new WeakReference<>(value));
        threadLocalCache.get().put(key, value);

        // Memory leak: never cleaned up
        GLOBAL_CACHE.put(key, value);

        // Pin some objects permanently (memory leak)
        if (pinnedObjects.size() < 1000) {
            pinnedObjects.add(value);
        }

        // Create unnecessary object references
        storeInNestedStructures(key, value);
    }

    public V get(K key) {
        // Check thread local first
        V threadLocalValue = threadLocalCache.get().get(key);
        if (threadLocalValue != null) {
            return threadLocalValue;
        }

        // Check main cache
        SoftReference<CacheEntry<V>> ref = cache.get(key);
        if (ref != null) {
            CacheEntry<V> entry = ref.get();
            if (entry != null) {
                // Update thread local - potential memory growth
                threadLocalCache.get().put(key, entry.value);
                return entry.value;
            }
        }

        // Check backup cache
        WeakReference<V> weakRef = backupCache.get(key);
        if (weakRef != null) {
            return weakRef.get();
        }

        return null;
    }

    private void storeInNestedStructures(K key, V value) {
        // Create deeply nested structure that's hard to GC
        Map<String, Map<String, Map<String, Object>>> nested = new HashMap<>();

        for (int i = 0; i < 100; i++) {
            String level1Key = "level1_" + i;
            Map<String, Map<String, Object>> level1 = new HashMap<>();

            for (int j = 0; j < 100; j++) {
                String level2Key = "level2_" + j;
                Map<String, Object> level2 = new HashMap<>();
                level2.put("key", key);
                level2.put("value", value);
                level2.put("circular", level2); // Circular reference

                level1.put(level2Key, level2);
            }
            nested.put(level1Key, level1);
        }

        // Store in global cache without cleanup
        GLOBAL_CACHE.put("nested_" + key, nested);
    }

    private void cleanup() {
        // Inefficient cleanup that creates more objects
        Reference<? extends CacheEntry<V>> ref;
        while ((ref = referenceQueue.poll()) != null) {
            // Create temporary objects during cleanup
            List<K> keysToRemove = new ArrayList<>();

            cache.entrySet().forEach(entry -> {
                if (entry.getValue() == ref) {
                    keysToRemove.add(entry.getKey());
                }
            });

            keysToRemove.forEach(cache::remove);
        }

        // Force GC (bad practice)
        if (Runtime.getRuntime().freeMemory() < Runtime.getRuntime().totalMemory() * 0.1) {
            System.gc();
        }
    }

    // Memory leak: finalizer that never completes
    @Override
    protected void finalize() throws Throwable {
        try {
            // Potentially long-running operation in finalizer
            Thread.sleep(1000);
            cleanupExecutor.shutdown();
        } finally {
            super.finalize();
        }
    }

    private static class CacheEntry<V> {
        final V value;
        final long timestamp;
        final List<V> references; // Unnecessary references

        CacheEntry(V value, long timestamp) {
            this.value = value;
            this.timestamp = timestamp;
            this.references = new ArrayList<>();

            // Create additional references
            for (int i = 0; i < 10; i++) {
                references.add(value);
            }
        }
    }
}
```

### Response
        """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Provide comprehensive JavaDoc documentation for this lock-free concurrent data structure. Include detailed explanation of the memory model guarantees, linearizability properties, ABA problem prevention, and performance characteristics:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class LockFreeTreiberStack<T> {
    private final AtomicReference<Node<T>> head = new AtomicReference<>();
    private final AtomicLong size = new AtomicLong(0);
    private final ThreadLocal<Node<T>> nodePool = new ThreadLocal<Node<T>>() {
        protected Node<T> initialValue() {
            return new Node<>(null, null);
        }
    };

    public void push(T item) {
        Node<T> newNode = getPooledNode(item);
        Node<T> currentHead;

        do {
            currentHead = head.get();
            newNode.next = currentHead;
        } while (!head.compareAndSet(currentHead, newNode));

        size.incrementAndGet();
    }

    public T pop() {
        Node<T> currentHead;
        Node<T> newHead;

        do {
            currentHead = head.get();
            if (currentHead == null) {
                return null;
            }
            newHead = currentHead.next;
        } while (!head.compareAndSet(currentHead, newHead));

        size.decrementAndGet();
        T result = currentHead.data;
        returnToPool(currentHead);
        return result;
    }

    public boolean isEmpty() {
        return head.get() == null;
    }

    public long size() {
        return size.get();
    }

    private Node<T> getPooledNode(T data) {
        Node<T> node = nodePool.get();
        if (node.data == null) {
            node.data = data;
            return node;
        }
        return new Node<>(data, null);
    }

    private void returnToPool(Node<T> node) {
        node.data = null;
        node.next = null;
        nodePool.set(node);
    }

    private static class Node<T> {
        volatile T data;
        volatile Node<T> next;

        Node(T data, Node<T> next) {
            this.data = data;
            this.next = next;
        }
    }
}
```

### Response
        """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Analyze this complex Java code and provide comprehensive documentation explaining the lock-free algorithm implementation:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class LockFreeQueue<T> {
    private static class Node<T> {
        volatile T item;
        volatile Node<T> next;
        Node(T item) { this.item = item; }
    }

    private static final AtomicReferenceFieldUpdater<Node, Node> nextUpdater =
        AtomicReferenceFieldUpdater.newUpdater(Node.class, Node.class, "next");
    private static final AtomicReferenceFieldUpdater<LockFreeQueue, Node> headUpdater =
        AtomicReferenceFieldUpdater.newUpdater(LockFreeQueue.class, Node.class, "head");
    private static final AtomicReferenceFieldUpdater<LockFreeQueue, Node> tailUpdater =
        AtomicReferenceFieldUpdater.newUpdater(LockFreeQueue.class, Node.class, "tail");

    private volatile Node<T> head;
    private volatile Node<T> tail;

    public LockFreeQueue() {
        Node<T> dummy = new Node<>(null);
        head = tail = dummy;
    }

    public void enqueue(T item) {
        Node<T> newNode = new Node<>(item);
        while (true) {
            Node<T> last = tail;
            Node<T> next = last.next;
            if (last == tail) {
                if (next == null) {
                    if (nextUpdater.compareAndSet(last, null, newNode)) {
                        tailUpdater.compareAndSet(this, last, newNode);
                        break;
                    }
                } else {
                    tailUpdater.compareAndSet(this, last, next);
                }
            }
        }
    }
}
```

Document the algorithm's correctness guarantees, memory consistency model, and potential ABA problem considerations.

### Response
        """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Document this advanced Java bytecode manipulation utility, explaining the dynamic class generation, method interception, and reflection optimization techniques:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class DynamicProxyGenerator {
    private static final String GENERATED_CLASS_PREFIX = "$Proxy";
    private static final AtomicInteger classCounter = new AtomicInteger(0);
    private static final Map<ClassLoader, Map<List<Class<?>>, Class<?>>> proxyCache = new ConcurrentHashMap<>();

    public static <T> T createProxy(Class<T> interfaceClass, InvocationHandler handler) {
        return createProxy(new Class<?>[]{interfaceClass}, handler, interfaceClass.getClassLoader());
    }

    @SuppressWarnings("unchecked")
    public static <T> T createProxy(Class<?>[] interfaces, InvocationHandler handler, ClassLoader loader) {
        List<Class<?>> key = Arrays.asList(interfaces);
        Class<?> proxyClass = proxyCache.computeIfAbsent(loader, k -> new ConcurrentHashMap<>())
            .computeIfAbsent(key, k -> generateProxyClass(interfaces, loader));

        try {
            Constructor<?> constructor = proxyClass.getConstructor(InvocationHandler.class);
            return (T) constructor.newInstance(handler);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create proxy instance", e);
        }
    }

    private static Class<?> generateProxyClass(Class<?>[] interfaces, ClassLoader loader) {
        String className = GENERATED_CLASS_PREFIX + classCounter.incrementAndGet();

        ClassWriter cw = new ClassWriter(ClassWriter.COMPUTE_MAXS | ClassWriter.COMPUTE_FRAMES);
        cw.visit(Opcodes.V11, Opcodes.ACC_PUBLIC | Opcodes.ACC_FINAL, className.replace('.', '/'), null, "java/lang/Object",
                    Arrays.stream(interfaces).map(c -> c.getName().replace('.', '/')).toArray(String[]::new));

        // Add InvocationHandler field
        cw.visitField(Opcodes.ACC_PRIVATE | Opcodes.ACC_FINAL, "handler", "Ljava/lang/reflect/InvocationHandler;", null, null);

        // Generate constructor
        generateConstructor(cw, className);

        // Generate method implementations
        for (Class<?> iface : interfaces) {
            for (Method method : iface.getMethods()) {
                generateMethodImpl(cw, className, method);
            }
        }

        cw.visitEnd();

        byte[] bytecode = cw.toByteArray();
        return defineClass(loader, className, bytecode);
    }
}
```

### Response
        """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Document this complex Java reflection-based dependency injection framework with circular dependency detection:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
@SuppressWarnings("unchecked")
public class DIContainer {
    private final Map<Class<?>, Object> singletons = new ConcurrentHashMap<>();
    private final Map<Class<?>, Class<?>> bindings = new ConcurrentHashMap<>();
    private final Map<Class<?>, Supplier<?>> factories = new ConcurrentHashMap<>();
    private final ThreadLocal<Set<Class<?>>> resolutionStack = ThreadLocal.withInitial(HashSet::new);

    public <T> DIContainer bind(Class<T> interfaceClass, Class<? extends T> implementationClass) {
        bindings.put(interfaceClass, implementationClass);
        return this;
    }

    public <T> DIContainer bindSingleton(Class<T> clazz, T instance) {
        singletons.put(clazz, instance);
        return this;
    }

    public <T> DIContainer bindFactory(Class<T> clazz, Supplier<T> factory) {
        factories.put(clazz, factory);
        return this;
    }

    public <T> T getInstance(Class<T> clazz) {
        Set<Class<?>> stack = resolutionStack.get();

        if (stack.contains(clazz)) {
            throw new CircularDependencyException("Circular dependency detected: " +
                stack.stream().map(Class::getSimpleName).collect(Collectors.joining(" -> ")) +
                " -> " + clazz.getSimpleName());
        }

        try {
            stack.add(clazz);
            return resolveInstance(clazz);
        } finally {
            stack.remove(clazz);
            if (stack.isEmpty()) {
                resolutionStack.remove();
            }
        }
    }

    private <T> T resolveInstance(Class<T> clazz) {
        // Check singletons first
        Object singleton = singletons.get(clazz);
        if (singleton != null) {
            return (T) singleton;
        }

        // Check factories
        Supplier<?> factory = factories.get(clazz);
        if (factory != null) {
            return (T) factory.get();
        }

        // Resolve implementation class
        Class<?> implClass = bindings.getOrDefault(clazz, clazz);

        if (implClass.isInterface() || Modifier.isAbstract(implClass.getModifiers())) {
            throw new DIException("Cannot instantiate interface or abstract class: " + implClass.getName());
        }

        return (T) createInstance(implClass);
    }

    private Object createInstance(Class<?> clazz) {
        Constructor<?> constructor = findInjectableConstructor(clazz);

        try {
            if (constructor.getParameterCount() == 0) {
                return constructor.newInstance();
            }

            Object[] args = new Object[constructor.getParameterCount()];
            Class<?>[] paramTypes = constructor.getParameterTypes();

            for (int i = 0; i < paramTypes.length; i++) {
                args[i] = getInstance(paramTypes[i]);
            }

            Object instance = constructor.newInstance(args);
            injectFields(instance);
            return instance;

        } catch (Exception e) {
            throw new DIException("Failed to create instance of " + clazz.getName(), e);
        }
    }

    private Constructor<?> findInjectableConstructor(Class<?> clazz) {
        Constructor<?>[] constructors = clazz.getDeclaredConstructors();

        // Look for @Inject annotated constructor
        for (Constructor<?> constructor : constructors) {
            if (constructor.isAnnotationPresent(Inject.class)) {
                constructor.setAccessible(true);
                return constructor;
            }
        }

        // Fall back to default constructor
        try {
            Constructor<?> defaultConstructor = clazz.getDeclaredConstructor();
            defaultConstructor.setAccessible(true);
            return defaultConstructor;
        } catch (NoSuchMethodException e) {
            throw new DIException("No injectable constructor found for " + clazz.getName());
        }
    }

    private void injectFields(Object instance) throws IllegalAccessException {
        Class<?> clazz = instance.getClass();

        while (clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                if (field.isAnnotationPresent(Inject.class)) {
                    field.setAccessible(true);
                    Object dependency = getInstance(field.getType());
                    field.set(instance, dependency);
                }
            }
            clazz = clazz.getSuperclass();
        }
    }
}
```

Explain the dependency resolution algorithm, circular dependency detection mechanism, and reflection optimization strategies.

### Response
            """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Document this sophisticated Java annotation processor that generates compile-time code for serialization optimization:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
@SupportedAnnotationTypes("com.example.FastSerializable")
@SupportedSourceVersion(SourceVersion.RELEASE_17)
public class SerializationProcessor extends AbstractProcessor {
    private static final String SERIALIZER_SUFFIX = "$FastSerializer";
    private TypeUtils typeUtils;
    private ElementUtils elementUtils;
    private Filer filer;

    @Override
    public synchronized void init(ProcessingEnvironment env) {
        super.init(env);
        this.typeUtils = env.getTypeUtils();
        this.elementUtils = env.getElementUtils();
        this.filer = env.getFiler();
    }

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        for (Element element : roundEnv.getElementsAnnotatedWith(FastSerializable.class)) {
            if (element.getKind() == ElementKind.CLASS) {
                TypeElement typeElement = (TypeElement) element;
                try {
                    generateSerializer(typeElement);
                } catch (IOException e) {
                    processingEnv.getMessager().printMessage(Diagnostic.Kind.ERROR,
                        "Failed to generate serializer: " + e.getMessage(), element);
                }
            }
        }
        return true;
    }

    private void generateSerializer(TypeElement typeElement) throws IOException {
        String className = typeElement.getQualifiedName().toString();
        String serializerName = className + SERIALIZER_SUFFIX;

        JavaFileObject file = filer.createSourceFile(serializerName, typeElement);

        try (PrintWriter writer = new PrintWriter(file.openWriter())) {
            generateSerializerClass(writer, typeElement, className, serializerName);
        }
    }

    private void generateSerializerClass(PrintWriter writer, TypeElement typeElement,
                                        String className, String serializerName) {
        List<? extends Element> fields = getSerializableFields(typeElement);

        writer.println("package " + getPackageName(className) + ";");
        writer.println();
        writer.println("import java.io.*;");
        writer.println("import java.nio.ByteBuffer;");
        writer.println();
        writer.println("public final class " + getSimpleName(serializerName) + " {");

        generateSerializeMethod(writer, fields, className);
        generateDeserializeMethod(writer, fields, className);
        generateSizeCalculation(writer, fields);

        writer.println("}");
    }
}
```

Explain the annotation processing lifecycle, compile-time code generation strategies, and performance optimization techniques used.

### Response
        """
    },
    {
        "type": "code_documentation",
        "prompt": """### Instruction
Document this advanced Java NIO.2 async file processing framework with custom completion handlers and buffer management:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class AsyncFileProcessor {
    private final AsynchronousFileChannel channel;
    private final ExecutorService executorService;
    private final ByteBufferPool bufferPool;
    private final CompletionHandler<Integer, ProcessingContext> readHandler;

    public AsyncFileProcessor(Path filePath, int bufferSize, int poolSize) throws IOException {
        Set<StandardOpenOption> options = EnumSet.of(StandardOpenOption.READ);
        this.executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        this.channel = AsynchronousFileChannel.open(filePath, options, executorService);
        this.bufferPool = new ByteBufferPool(bufferSize, poolSize);
        this.readHandler = new AsyncReadHandler();
    }

    public CompletableFuture<ProcessingResult> processFile(FileProcessor processor) {
        CompletableFuture<ProcessingResult> future = new CompletableFuture<>();
        ProcessingContext context = new ProcessingContext(processor, future, 0);

        startAsyncRead(context);
        return future;
    }

    private void startAsyncRead(ProcessingContext context) {
        ByteBuffer buffer = bufferPool.acquire();
        context.setCurrentBuffer(buffer);

        channel.read(buffer, context.getPosition(), context, readHandler);
    }

    private class AsyncReadHandler implements CompletionHandler<Integer, ProcessingContext> {
        @Override
        public void completed(Integer bytesRead, ProcessingContext context) {
            if (bytesRead == -1) {
                // End of file
                context.getFuture().complete(context.getResult());
                bufferPool.release(context.getCurrentBuffer());
                return;
            }

            ByteBuffer buffer = context.getCurrentBuffer();
            buffer.flip();

            try {
                ProcessingResult chunkResult = context.getProcessor().processChunk(buffer);
                context.mergeResult(chunkResult);

                buffer.clear();
                context.advancePosition(bytesRead);

                // Continue reading
                channel.read(buffer, context.getPosition(), context, this);

            } catch (Exception e) {
                context.getFuture().completeExceptionally(e);
                bufferPool.release(buffer);
            }
        }

        @Override
        public void failed(Throwable exc, ProcessingContext context) {
            context.getFuture().completeExceptionally(exc);
            bufferPool.release(context.getCurrentBuffer());
        }
    }

    private static class ProcessingContext {
        private final FileProcessor processor;
        private final CompletableFuture<ProcessingResult> future;
        private final ProcessingResult result;
        private ByteBuffer currentBuffer;
        private long position;

        ProcessingContext(FileProcessor processor, CompletableFuture<ProcessingResult> future, long initialPosition) {
            this.processor = processor;
            this.future = future;
            this.result = new ProcessingResult();
            this.position = initialPosition;
        }

        // Getters and setters...
    }
}
```

Explain the async I/O patterns, buffer lifecycle management, and error handling strategies used in this implementation.

### Response
        """
    },
    {
        "type": "code_fix",
        "prompt": """### Instruction
Fix the critical bugs in this Java reactive streams implementation. The code has memory leaks, race conditions, and incorrect backpressure handling:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class CustomPublisher<T> implements Publisher<T> {
    private final List<T> data;
    private final Set<Subscription> subscriptions = new HashSet<>();

    public CustomPublisher(List<T> data) {
        this.data = data;
    }

    @Override
    public void subscribe(Subscriber<? super T> subscriber) {
        CustomSubscription subscription = new CustomSubscription(subscriber);
        subscriptions.add(subscription);
        subscriber.onSubscribe(subscription);

        for (T item : data) {
            subscriber.onNext(item);
        }
        subscriber.onComplete();
    }

    private class CustomSubscription implements Subscription {
        private final Subscriber<? super T> subscriber;
        private boolean cancelled = false;
        private int index = 0;

        CustomSubscription(Subscriber<? super T> subscriber) {
            this.subscriber = subscriber;
        }

        @Override
        public void request(long n) {
            if (n <= 0) {
                subscriber.onError(new IllegalArgumentException());
                return;
            }

            while (n > 0 && index < data.size() && !cancelled) {
                subscriber.onNext(data.get(index++));
                n--;
            }

            if (index >= data.size()) {
                subscriber.onComplete();
            }
        }

        @Override
        public void cancel() {
            cancelled = true;
        }
    }
}
```

### Response
        """
    },
    {
        "type": "code_fix",
        "prompt": """### Instruction
Fix the complex bugs in this Java custom ClassLoader implementation that handles hot-swapping and dependency resolution:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class HotSwapClassLoader extends ClassLoader {
    private final Map<String, byte[]> classBytes = new HashMap<>();
    private final Map<String, Class<?>> loadedClasses = new HashMap<>();
    private final Set<String> packagePrefixes;

    public HotSwapClassLoader(ClassLoader parent, Set<String> packagePrefixes) {
        super(parent);
        this.packagePrefixes = packagePrefixes;
    }

    public void updateClass(String className, byte[] newBytecode) {
        classBytes.put(className, newBytecode);
        loadedClasses.remove(className);

        // Force reload of dependent classes
        for (String loadedClass : loadedClasses.keySet()) {
            if (isDependentOn(loadedClass, className)) {
                loadedClasses.remove(loadedClass);
            }
        }
    }

    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        Class<?> clazz = loadedClasses.get(name);
        if (clazz != null) {
            return clazz;
        }

        if (shouldLoadLocally(name)) {
            byte[] bytecode = classBytes.get(name);
            if (bytecode != null) {
                clazz = defineClass(name, bytecode, 0, bytecode.length);
                loadedClasses.put(name, clazz);

                if (resolve) {
                    resolveClass(clazz);
                }
                return clazz;
            }
        }

        return super.loadClass(name, resolve);
    }

    private boolean shouldLoadLocally(String className) {
        return packagePrefixes.stream().anyMatch(className::startsWith);
    }

    private boolean isDependentOn(String className, String dependency) {
        try {
            ClassReader reader = new ClassReader(classBytes.get(className));
            DependencyVisitor visitor = new DependencyVisitor(dependency);
            reader.accept(visitor, 0);
            return visitor.hasDependency();
        } catch (Exception e) {
            return false;
        }
    }
}
```

### Response
        """
    },
    {
        "type": "code_fix",
        "prompt": """### Instruction
Fix the critical concurrency and memory management issues in this Java off-heap memory allocator implementation:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class OffHeapAllocator {
    private static final int BLOCK_SIZE = 1024 * 1024; // 1MB blocks
    private final long baseAddress;
    private final long totalSize;
    private final BitSet freeBlocks;
    private final Object allocationLock = new Object();
    private final Map<Long, Integer> allocations = new HashMap<>();

    public OffHeapAllocator(long sizeInBytes) {
        this.totalSize = sizeInBytes;
        this.baseAddress = allocateNativeMemory(sizeInBytes);
        this.freeBlocks = new BitSet((int) (sizeInBytes / BLOCK_SIZE));
        this.freeBlocks.set(0, (int) (sizeInBytes / BLOCK_SIZE));
    }

    public long allocate(int size) {
        int blocksNeeded = (size + BLOCK_SIZE - 1) / BLOCK_SIZE;

        synchronized (allocationLock) {
            int startBlock = findFreeBlocks(blocksNeeded);
            if (startBlock == -1) {
                return 0; // Out of memory
            }

            for (int i = startBlock; i < startBlock + blocksNeeded; i++) {
                freeBlocks.clear(i);
            }

            long address = baseAddress + (startBlock * BLOCK_SIZE);
            allocations.put(address, blocksNeeded);
            return address;
        }
    }

    public void deallocate(long address) {
        synchronized (allocationLock) {
            Integer blocksAllocated = allocations.remove(address);
            if (blocksAllocated == null) {
                throw new IllegalArgumentException("Invalid address: " + address);
            }

            int startBlock = (int) ((address - baseAddress) / BLOCK_SIZE);
            for (int i = startBlock; i < startBlock + blocksAllocated; i++) {
                freeBlocks.set(i);
            }
        }
    }

    private int findFreeBlocks(int blocksNeeded) {
        int consecutiveFree = 0;
        int startIndex = 0;

        for (int i = 0; i < freeBlocks.size(); i++) {
            if (freeBlocks.get(i)) {
                if (consecutiveFree == 0) {
                    startIndex = i;
                }
                consecutiveFree++;

                if (consecutiveFree >= blocksNeeded) {
                    return startIndex;
                }
            } else {
                consecutiveFree = 0;
            }
        }

        return -1;
    }

    private native long allocateNativeMemory(long size);
    private native void freeNativeMemory(long address);

    public void close() {
        freeNativeMemory(baseAddress);
    }
}
```

### Response
        """
    },
    {
        "type": "code_fix",
        "prompt": """### Instruction

Analyze and fix the performance bottlenecks and thread-safety issues in the following Java custom thread pool implementation with work-stealing capabilities.

Your response must:

- Identify **concrete performance bottlenecks** and **thread safety issues** in the code.
- Clearly explain the **root cause** of each issue (e.g., contention, race condition, busy waiting, unsafe publication, etc.).
- Provide an improved version of the code with the following constraints:
  - **Preserve work-stealing** behavior.
  - Ensure proper **thread-safe access** to shared data structures.
  - Avoid **busy waiting** and minimize CPU usage under idle conditions.
  - Ensure clean **shutdown** and **task completion guarantees**.

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class WorkStealingThreadPool implements ExecutorService {
    private final int parallelism;
    private final WorkerThread[] workers;
    private final Random random = new Random();
    private volatile boolean shutdown = false;
    private final CountDownLatch terminationLatch;

    public WorkStealingThreadPool(int parallelism) {
        this.parallelism = parallelism;
        this.workers = new WorkerThread[parallelism];
        this.terminationLatch = new CountDownLatch(parallelism);

        for (int i = 0; i < parallelism; i++) {
            workers[i] = new WorkerThread(i);
            workers[i].start();
        }
    }

    @Override
    public void execute(Runnable command) {
        if (shutdown) {
            throw new RejectedExecutionException("ThreadPool is shutdown");
        }

        // Try to submit to current thread's queue first
        Thread currentThread = Thread.currentThread();
        if (currentThread instanceof WorkerThread) {
            WorkerThread worker = (WorkerThread) currentThread;
            if (worker.submitTask(command)) {
                return;
            }
        }

        // Find a worker with space
        for (WorkerThread worker : workers) {
            if (worker.submitTask(command)) {
                return;
            }
        }

        // All queues full, block until space available
        while (!shutdown) {
            for (WorkerThread worker : workers) {
                if (worker.submitTask(command)) {
                    return;
                }
            }
            Thread.yield();
        }
    }

    private class WorkerThread extends Thread {
        private final int workerId;
        private final Deque<Runnable> workQueue = new ArrayDeque<>();
        private final Object queueLock = new Object();

        WorkerThread(int workerId) {
            this.workerId = workerId;
            setName("WorkStealing-" + workerId);
        }

        boolean submitTask(Runnable task) {
            synchronized (queueLock) {
                if (workQueue.size() < 1000) {
                    workQueue.addLast(task);
                    queueLock.notify();
                    return true;
                }
                return false;
            }
        }

        @Override
        public void run() {
            try {
                while (!shutdown || !workQueue.isEmpty()) {
                    Runnable task = getTask();
                    if (task != null) {
                        task.run();
                    } else {
                        // Try to steal work
                        task = stealWork();
                        if (task != null) {
                            task.run();
                        } else {
                            Thread.sleep(1);
                        }
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } finally {
                terminationLatch.countDown();
            }
        }

        private Runnable getTask() {
            synchronized (queueLock) {
                return workQueue.pollFirst();
            }
        }

        private Runnable stealWork() {
            int victim = random.nextInt(parallelism);
            if (victim == workerId) {
                return null;
            }

            WorkerThread victimWorker = workers[victim];
            synchronized (victimWorker.queueLock) {
                return victimWorker.workQueue.pollLast();
            }
        }
    }

    @Override
    public void shutdown() {
        shutdown = true;
        for (WorkerThread worker : workers) {
            worker.interrupt();
        }
    }
}
```

### Response
        """
    }
]

query_args = {
    "error_analysis": {"temperature": 0.2, "max_tokens": 1024},
    "code_documentation": {"temperature": 0.3, "max_tokens": 1024},
    "code_fix": {"temperature": 0.2, "max_tokens": 1024},
}


def test_questions_batch(questions=test_questions, save_path="batch_test_results.json"):
    results = []

    print(f"📦 Running {len(questions)} test cases...")
    for q in tqdm(questions, unit="question"):
        payload = {
            "prompt": q["prompt"],
            "max_tokens": query_args[q["type"]]["max_tokens"],
            "temperature": query_args[q["type"]]["temperature"],
            "top_p": 0.9,
            "stop_words": [ "<|endoftext|>", "```/", "---" ]
        }

        try:
            response = requests.post(API_URL, json=payload, timeout=180)
            response.raise_for_status()
            res_json = response.json()

            results.append(
                {
                    "prompt": res_json.get("prompt", ""),
                    "response": res_json.get("response", ""),
                    "elapsed_time_sec": res_json.get("query_time", 0),
                    "max_tokens": res_json.get("max_tokens", ""),
                    "top_p": res_json.get("top_p", ""),
                    "temperature": res_json.get("temperature", ""),
                    "completion_tokens": res_json.get("completion_tokens", 0),
                    "tokens_per_second": res_json.get("tokens_per_second", 0)
                }
            )

        except Exception as e:
            results.append(
                {
                    "prompt": q["prompt"],
                    "response": f"ERROR: {str(e)}",
                    "elapsed_time_sec": -1,
                    "max_tokens": 0,
                    "top_p": 0,
                    "temperature": 0,
                    "completion_tokens": 0,
                    "tokens_per_second": 0
                }
            )

    with open(save_path, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n✅ Done. Results saved to {save_path}")


if __name__ == "__main__":
    test_questions_batch()

