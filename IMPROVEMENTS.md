# StarCoder2 15B Prompt Improvements

## Vấn đề ban đ<PERSON>u

<PERSON>ết quả từ model StarCoder2 15B không chính xác khi phân tích lỗi concurrency trong Java code:
- Model trả về code "sửa" thay vì phân tích chi tiết
- Không xác định được các race condition cụ thể
- Thiếu giải thích về deadlock potential
- Không đề cập đến memory visibility issues

## Các cải thiện đã thực hiện

### 1. Phân loại Task (Task Classification)

Hệ thống tự động phân loại 3 loại câu hỏi:

- **error_analysis**: Phân tích lỗi, bug, vulnerability
- **code_documentation**: Tạo tài li<PERSON>u, giải thích code  
- **code_fix**: Sửa lỗi, cải thiện code

```python
def classify_question(prompt: str, context: str = None) -> str:
    # Tự động phân loại dựa trên keywords trong prompt
```

### 2. Enhanced Prompts

Mỗi loại task có prompt template được tối ưu riêng:

#### Error Analysis Prompt:
- Yêu cầu format output cụ thể
- Tập trung vào việc phân tích, KHÔNG sửa code
- Đòi hỏi giải thích chi tiết về location, problem, consequence, root cause

#### Code Documentation Prompt:
- Tập trung vào giải thích chức năng
- Yêu cầu ví dụ sử dụng
- Format documentation rõ ràng

#### Code Fix Prompt:
- Xác định vấn đề trước khi sửa
- Cung cấp code đã sửa
- Giải thích từng thay đổi

### 3. Optimized Parameters

Mỗi task type có parameters được tối ưu riêng:

```python
# Error Analysis
temperature: 0.4    # Cao hơn để phân tích chi tiết
top_p: 0.85        # Tập trung hơn
max_tokens: 1500   # Nhiều không gian cho phân tích

# Code Documentation  
temperature: 0.3    # Cân bằng cho docs rõ ràng
top_p: 0.9         # Cho phép sáng tạo trong giải thích
max_tokens: 2000   # Nhiều không gian cho docs đầy đủ

# Code Fix
temperature: 0.2    # Rất tập trung cho fixes chính xác
top_p: 0.8         # Deterministic hơn
max_tokens: 1800   # Không gian cho code + giải thích
```

### 4. Structured Output

Yêu cầu format output có cấu trúc:

```
## Critical Issues Found:

### Issue 1: Race Condition
**Location**: totalVolume field in processMarketData()
**Problem**: Non-atomic increment operation
**Consequence**: Data corruption in multi-threaded environment
**Root Cause**: Missing synchronization on shared mutable state

### Issue 2: Deadlock Potential
**Location**: Lock ordering between positions and dataLock
**Problem**: Inconsistent lock acquisition order
**Consequence**: Potential deadlock between threads
**Root Cause**: Different lock ordering in different methods
```

## Cách sử dụng

### 1. Chạy server:
```bash
python run_llama_starcoder2_15b_q8.py
```

### 2. Test classification:
```bash
python test_classification.py
```

### 3. Gửi request:
```python
import requests

response = requests.post("http://localhost:8000/generate", json={
    "prompt": "Analyze this Java code for concurrency bugs: [code]",
    "max_tokens": 1500,
    "temperature": 0.4
})

result = response.json()
print(f"Task Type: {result['type']}")
print(f"Response: {result['response']}")
```

## Kết quả mong đợi

Với các cải thiện này, model sẽ:

1. **Phân loại đúng task type** dựa trên nội dung prompt
2. **Sử dụng prompt template phù hợp** cho từng loại task
3. **Áp dụng parameters tối ưu** cho từng task
4. **Trả về kết quả có cấu trúc** và chi tiết hơn
5. **Tập trung vào phân tích** thay vì chỉ sửa code

## So sánh kết quả

### Trước khi cải thiện:
- Trả về code "sửa" đơn giản
- Không giải thích vấn đề cụ thể
- Thiếu phân tích chi tiết

### Sau khi cải thiện:
- Phân tích chi tiết từng vấn đề
- Giải thích root cause và consequence
- Format có cấu trúc, dễ đọc
- Tập trung vào việc phân tích thay vì sửa code

## Files đã thay đổi

- `run_llama_starcoder2_15b_q8.py`: Main server với các cải thiện
- `test_classification.py`: Test script cho 3 loại task
- `IMPROVEMENTS.md`: Tài liệu này

## Lưu ý

- Hệ thống tự động detect task type, không cần chỉ định manual
- Parameters được tối ưu tự động dựa trên task type
- Có thể override parameters trong request nếu cần
- Enhanced prompt được return trong response để debug
