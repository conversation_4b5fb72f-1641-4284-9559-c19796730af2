# StarCoder2 15B Prompt Improvements với Alpaca Format

## Vấn đề ban đầu

Kết quả từ model StarCoder2 15B không chính xác khi phân tích lỗi concurrency trong Java code:
- Model trả về code "sửa" thay vì phân tích chi tiết
- Không xác định được các race condition cụ thể
- Thiếu giải thích về deadlock potential
- Không đề cập đến memory visibility issues
- **Không sử dụng đúng Alpaca format mà StarCoder2 được train**

## Các cải thiện đã thực hiện

### 🎯 **Alpaca Format Implementation**

StarCoder2 được train với Alpaca format, vì vậy tất cả prompts đã được chuyển đổi sang format:

```
### Instruction:
[Detailed instruction for the task]

### Input:
[Input data/code to analyze]

### Response:
[Model response goes here]
```

### 1. <PERSON>ân loại Task (Task Classification)

Hệ thống tự động phân loại 3 loại câu hỏi:

- **error_analysis**: <PERSON>ân tích lỗi, bug, vulnerability
- **code_documentation**: Tạo tài liệu, giải thích code  
- **code_fix**: Sửa lỗi, cải thiện code

```python
def classify_question(prompt: str, context: str = None) -> str:
    # Tự động phân loại dựa trên keywords trong prompt
```

### 2. Enhanced Prompts với Alpaca Format

Mỗi loại task có prompt template được tối ưu riêng theo Alpaca format:

#### Error Analysis Prompt:
```
### Instruction:
You are an expert code analyzer. Analyze the provided Java code and identify ALL critical bugs, concurrency issues, race conditions, deadlock potential, and memory visibility problems.

IMPORTANT:
- Do NOT provide fixed code
- Focus ONLY on identifying and explaining problems
- Be specific about methods, variables, and line references

### Input:
Java code to analyze:
[code here]

### Response:
```

#### Code Documentation Prompt:
```
### Instruction:
You are an expert technical writer. Create clear, comprehensive documentation for the provided code.

Requirements:
- Explain what the code does in plain language
- Document all methods, classes, and important variables
- Include usage examples where appropriate

### Input:
Code to document:
[code here]

### Response:
```

#### Code Fix Prompt:
```
### Instruction:
You are an expert software engineer. Identify problems in the provided code and provide corrected versions.

Requirements:
- First identify what's wrong
- Then provide the corrected code
- Explain what changes were made and why

### Input:
Code to fix:
[code here]

### Response:
```

### 3. Optimized Parameters

Mỗi task type có parameters được tối ưu riêng:

```python
# Error Analysis
temperature: 0.4    # Cao hơn để phân tích chi tiết
top_p: 0.85        # Tập trung hơn
max_tokens: 1500   # Nhiều không gian cho phân tích

# Code Documentation  
temperature: 0.3    # Cân bằng cho docs rõ ràng
top_p: 0.9         # Cho phép sáng tạo trong giải thích
max_tokens: 2000   # Nhiều không gian cho docs đầy đủ

# Code Fix
temperature: 0.2    # Rất tập trung cho fixes chính xác
top_p: 0.8         # Deterministic hơn
max_tokens: 1800   # Không gian cho code + giải thích
```

### 4. Structured Output

Yêu cầu format output có cấu trúc:

```
## Critical Issues Found:

### Issue 1: Race Condition
**Location**: totalVolume field in processMarketData()
**Problem**: Non-atomic increment operation
**Consequence**: Data corruption in multi-threaded environment
**Root Cause**: Missing synchronization on shared mutable state

### Issue 2: Deadlock Potential
**Location**: Lock ordering between positions and dataLock
**Problem**: Inconsistent lock acquisition order
**Consequence**: Potential deadlock between threads
**Root Cause**: Different lock ordering in different methods
```

## Cách sử dụng

### 1. Chạy server:
```bash
python run_llama_starcoder2_15b_q8.py
```

### 2. Test classification:
```bash
python test_classification.py
```

### 3. Test Alpaca format:
```bash
python test_alpaca_format.py
```

### 4. Gửi request:
```python
import requests

response = requests.post("http://localhost:8000/generate", json={
    "prompt": "Analyze this Java code for concurrency bugs: [code]",
    "max_tokens": 1500,
    "temperature": 0.4
})

result = response.json()
print(f"Task Type: {result['type']}")
print(f"Response: {result['response']}")
```

## Kết quả mong đợi

Với các cải thiện này, model sẽ:

1. **Sử dụng đúng Alpaca format** mà StarCoder2 được train
2. **Phân loại đúng task type** dựa trên nội dung prompt
3. **Sử dụng prompt template phù hợp** cho từng loại task
4. **Áp dụng parameters tối ưu** cho từng task
5. **Trả về kết quả có cấu trúc** và chi tiết hơn
6. **Tập trung vào phân tích** thay vì chỉ sửa code

## So sánh kết quả

### Trước khi cải thiện:
- Không sử dụng đúng Alpaca format
- Trả về code "sửa" đơn giản
- Không giải thích vấn đề cụ thể
- Thiếu phân tích chi tiết

### Sau khi cải thiện:
- **Sử dụng đúng Alpaca format** (### Instruction/Input/Response)
- Phân tích chi tiết từng vấn đề
- Giải thích root cause và consequence
- Format có cấu trúc, dễ đọc
- Tập trung vào việc phân tích thay vì sửa code

## Files đã thay đổi

- `run_llama_starcoder2_15b_q8.py`: Main server với Alpaca format và các cải thiện
- `test_classification.py`: Test script cho 3 loại task
- `test_alpaca_format.py`: Test script cho Alpaca format
- `IMPROVEMENTS.md`: Tài liệu này

## Lưu ý

- **Alpaca format được áp dụng tự động** cho tất cả prompts
- Hệ thống tự động detect task type, không cần chỉ định manual
- Parameters được tối ưu tự động dựa trên task type
- Có thể override parameters trong request nếu cần
- Enhanced prompt được return trong response để debug
- StarCoder2 sẽ hoạt động tốt hơn với đúng format này
