import requests
import json
import time

API_URL = "http://localhost:8000/generate"

# Test case với code có lỗi concurrency
test_concurrency_analysis = {
    "prompt": """### Instruction

Analyze the following Java code for critical concurrency bugs, memory visibility issues, and JMM violations. Identify all race conditions, deadlock potential, and explain the specific consequences of each issue:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class HighFrequencyTrader {
    private volatile boolean isActive = false;
    private long totalVolume = 0;
    private final AtomicReference<MarketData> latestData = new AtomicReference<>();
    private final ReentrantReadWriteLock dataLock = new ReentrantReadWriteLock();
    private final ConcurrentHashMap<String, Position> positions = new ConcurrentHashMap<>();
    private static final Object STATIC_LOCK = new Object();

    public void startTrading() {
        synchronized (STATIC_LOCK) {
            if (!isActive) {
                isActive = true;
                new Thread(this::processMarketData).start();
                new Thread(this::executeOrders).start();
            }
        }
    }

    private void processMarketData() {
        while (isActive) {
            dataLock.writeLock().lock();
            try {
                MarketData newData = fetchMarketData();
                latestData.set(newData);
                totalVolume += newData.getVolume();

                synchronized (positions) {
                    updatePositions(newData);
                }
            } finally {
                dataLock.writeLock().unlock();
            }
        }
    }

    private void executeOrders() {
        while (isActive) {
            synchronized (positions) {
                dataLock.readLock().lock();
                try {
                    MarketData data = latestData.get();
                    if (data != null && shouldExecute(data)) {
                        synchronized (STATIC_LOCK) {
                            executeOrder(data);
                            totalVolume -= data.getVolume();
                        }
                    }
                } finally {
                    dataLock.readLock().unlock();
                }
            }
        }
    }

    public long getTotalVolume() {
        return totalVolume;
    }
}
```

### Response""",
    "max_tokens": 1500,
    "temperature": 0.4,
    "top_p": 0.85
}

def test_improved_analysis():
    print("🧪 Testing improved concurrency analysis...")
    print("=" * 60)
    
    try:
        response = requests.post(API_URL, json=test_concurrency_analysis)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Task Type: {result.get('type', 'unknown')}")
            print(f"📊 Completion tokens: {result.get('completion_tokens', 0)}")
            print(f"⏱️  Query time: {result.get('query_time', 0):.2f}s")
            print(f"🚀 Tokens/sec: {result.get('tokens_per_second', 0):.2f}")
            print("\n" + "=" * 60)
            print("📝 RESPONSE:")
            print("=" * 60)
            print(result.get('response', ''))
            print("=" * 60)
            
            # Analyze response quality
            response_text = result.get('response', '').lower()
            quality_indicators = {
                'mentions_race_conditions': 'race condition' in response_text or 'race' in response_text,
                'mentions_deadlock': 'deadlock' in response_text,
                'mentions_memory_visibility': 'memory visibility' in response_text or 'volatile' in response_text,
                'mentions_totalvolume_issue': 'totalvolume' in response_text,
                'structured_format': '###' in result.get('response', '') or '**' in result.get('response', ''),
                'specific_locations': 'line' in response_text or 'method' in response_text,
                'explains_consequences': 'consequence' in response_text or 'impact' in response_text or 'result' in response_text
            }
            
            print("\n🔍 QUALITY ANALYSIS:")
            print("=" * 40)
            for indicator, found in quality_indicators.items():
                status = "✅" if found else "❌"
                print(f"{status} {indicator.replace('_', ' ').title()}: {found}")
            
            quality_score = sum(quality_indicators.values()) / len(quality_indicators) * 100
            print(f"\n📈 Overall Quality Score: {quality_score:.1f}%")
            
            if quality_score >= 70:
                print("🎉 EXCELLENT - Analysis meets quality standards!")
            elif quality_score >= 50:
                print("👍 GOOD - Analysis is decent but could be improved")
            else:
                print("⚠️  NEEDS IMPROVEMENT - Analysis lacks important details")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_improved_analysis()
