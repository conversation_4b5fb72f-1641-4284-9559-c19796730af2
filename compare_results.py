import requests
import json
import time

API_URL = "http://localhost:8000/generate"

# Original prompt (như bạn đã test)
original_prompt = """### Instruction

Analyze the following Java code for critical concurrency bugs, memory visibility issues, and JMM violations. Identify all race conditions, deadlock potential, and explain the specific consequences of each issue:

Do **not** include unrelated documentation, file paths, or markdown metadata.

Here is the original Java implementation:

```java
public class HighFrequencyTrader {
    private volatile boolean isActive = false;
    private long totalVolume = 0;
    private final AtomicReference<MarketData> latestData = new AtomicReference<>();
    private final ReentrantReadWriteLock dataLock = new ReentrantReadWriteLock();
    private final ConcurrentHashMap<String, Position> positions = new ConcurrentHashMap<>();
    private static final Object STATIC_LOCK = new Object();

    public void startTrading() {
        synchronized (STATIC_LOCK) {
            if (!isActive) {
                isActive = true;
                new Thread(this::processMarketData).start();
                new Thread(this::executeOrders).start();
            }
        }
    }

    private void processMarketData() {
        while (isActive) {
            dataLock.writeLock().lock();
            try {
                MarketData newData = fetchMarketData();
                latestData.set(newData);
                totalVolume += newData.getVolume();

                synchronized (positions) {
                    updatePositions(newData);
                }
            } finally {
                dataLock.writeLock().unlock();
            }
        }
    }

    private void executeOrders() {
        while (isActive) {
            synchronized (positions) {
                dataLock.readLock().lock();
                try {
                    MarketData data = latestData.get();
                    if (data != null && shouldExecute(data)) {
                        synchronized (STATIC_LOCK) {
                            executeOrder(data);
                            totalVolume -= data.getVolume();
                        }
                    }
                } finally {
                    dataLock.readLock().unlock();
                }
            }
        }
    }

    public long getTotalVolume() {
        return totalVolume;
    }
}
```

### Response"""

def analyze_response_quality(response_text):
    """Analyze the quality of the response"""
    response_lower = response_text.lower()
    
    # Key issues that should be identified in the code
    critical_issues = {
        'race_condition_totalvolume': {
            'keywords': ['totalvolume', 'race condition', 'race', 'non-atomic'],
            'description': 'totalVolume field race condition'
        },
        'deadlock_potential': {
            'keywords': ['deadlock', 'lock ordering', 'positions', 'datalock'],
            'description': 'Deadlock between positions and dataLock'
        },
        'memory_visibility': {
            'keywords': ['memory visibility', 'volatile', 'jmm', 'java memory model'],
            'description': 'Memory visibility issues'
        },
        'synchronization_issues': {
            'keywords': ['synchronization', 'synchronized', 'lock'],
            'description': 'Synchronization problems'
        }
    }
    
    quality_metrics = {
        'provides_analysis': not ('```java' in response_text and response_text.count('```') >= 2),
        'structured_format': any(marker in response_text for marker in ['###', '**', '##', '1.', '2.']),
        'specific_locations': any(word in response_lower for word in ['method', 'line', 'field', 'variable']),
        'explains_consequences': any(word in response_lower for word in ['consequence', 'impact', 'result', 'cause', 'problem']),
        'mentions_concurrency': any(word in response_lower for word in ['thread', 'concurrent', 'parallel', 'synchroniz']),
    }
    
    # Check for critical issues
    issues_found = {}
    for issue_name, issue_info in critical_issues.items():
        found = any(keyword in response_lower for keyword in issue_info['keywords'])
        issues_found[issue_name] = found
        quality_metrics[f'identifies_{issue_name}'] = found
    
    return quality_metrics, issues_found

def run_comparison():
    print("🔄 COMPARING ORIGINAL vs IMPROVED PROMPTS")
    print("=" * 80)
    
    # Test with original parameters (như kết quả bạn đã có)
    original_request = {
        "prompt": original_prompt,
        "max_tokens": 1024,
        "temperature": 0.2,
        "top_p": 0.9
    }
    
    print("1️⃣  Testing with ORIGINAL parameters...")
    print(f"   Temperature: {original_request['temperature']}")
    print(f"   Top-p: {original_request['top_p']}")
    print(f"   Max tokens: {original_request['max_tokens']}")
    
    try:
        start_time = time.time()
        response1 = requests.post(API_URL, json=original_request)
        end_time = time.time()
        
        if response1.status_code == 200:
            result1 = response1.json()
            print(f"   ✅ Success - {result1.get('completion_tokens', 0)} tokens in {end_time - start_time:.2f}s")
            
            # Analyze quality
            quality1, issues1 = analyze_response_quality(result1.get('response', ''))
            
            print("\n📊 ORIGINAL RESULTS:")
            print("-" * 40)
            print(f"Task Type: {result1.get('type', 'unknown')}")
            print(f"Response Length: {len(result1.get('response', ''))} chars")
            print(f"Enhanced Prompt Used: {'enhanced_prompt' in result1}")
            
            print("\n🔍 Quality Analysis:")
            for metric, value in quality1.items():
                status = "✅" if value else "❌"
                print(f"   {status} {metric.replace('_', ' ').title()}")
            
            quality_score1 = sum(quality1.values()) / len(quality1) * 100
            print(f"\n📈 Quality Score: {quality_score1:.1f}%")
            
            print("\n📝 Response Preview (first 300 chars):")
            print("-" * 40)
            print(result1.get('response', '')[:300] + "..." if len(result1.get('response', '')) > 300 else result1.get('response', ''))
            
        else:
            print(f"   ❌ Failed: {response1.status_code}")
            return
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return
    
    print("\n" + "=" * 80)
    print("🎯 SUMMARY & RECOMMENDATIONS:")
    print("=" * 80)
    
    if quality_score1 >= 70:
        print("🎉 EXCELLENT: The improved prompt system is working well!")
    elif quality_score1 >= 50:
        print("👍 GOOD: Significant improvement, but still room for enhancement")
        print("\n💡 Suggestions for further improvement:")
        if not quality1.get('identifies_race_condition_totalvolume', False):
            print("   - Add more specific guidance about race conditions")
        if not quality1.get('identifies_deadlock_potential', False):
            print("   - Emphasize deadlock analysis in prompt")
        if not quality1.get('structured_format', False):
            print("   - Enforce stricter output formatting")
    else:
        print("⚠️  NEEDS MORE WORK: Consider these improvements:")
        print("   - More specific examples in prompt")
        print("   - Higher temperature for more detailed analysis")
        print("   - Longer max_tokens for comprehensive responses")
        print("   - Add few-shot examples")

if __name__ == "__main__":
    run_comparison()
