import requests
import json

API_URL = "http://localhost:8000/generate"

# Test cases for 3 different types
test_cases = [
    {
        "name": "Error Analysis Test",
        "expected_type": "error_analysis",
        "prompt": """### Instruction

Analyze the following Java code for critical concurrency bugs, memory visibility issues, and JMM violations. Identify all race conditions, deadlock potential, and explain the specific consequences of each issue:

```java
public class HighFrequencyTrader {
    private volatile boolean isActive = false;
    private long totalVolume = 0;
    
    public void startTrading() {
        if (!isActive) {
            isActive = true;
            new Thread(this::processMarketData).start();
        }
    }
    
    private void processMarketData() {
        while (isActive) {
            totalVolume += fetchData();
        }
    }
    
    public long getTotalVolume() {
        return totalVolume;
    }
}
```

### Response"""
    },
    
    {
        "name": "Code Documentation Test", 
        "expected_type": "code_documentation",
        "prompt": """Please document the following Java class and explain what it does:

```java
public class UserManager {
    private Map<String, User> users = new HashMap<>();
    
    public void addUser(String id, User user) {
        users.put(id, user);
    }
    
    public User getUser(String id) {
        return users.get(id);
    }
    
    public boolean removeUser(String id) {
        return users.remove(id) != null;
    }
}
```

Explain the purpose, methods, and how to use this class."""
    },
    
    {
        "name": "Code Fix Test",
        "expected_type": "code_fix", 
        "prompt": """Fix the following Java code that has thread safety issues:

```java
public class Counter {
    private int count = 0;
    
    public void increment() {
        count++;
    }
    
    public int getCount() {
        return count;
    }
}
```

Please provide the corrected version and explain what changes were made."""
    }
]

def test_classification():
    print("🧪 Testing Task Classification System")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣  {test_case['name']}")
        print(f"Expected Type: {test_case['expected_type']}")
        print("-" * 40)
        
        try:
            response = requests.post(API_URL, json={
                "prompt": test_case["prompt"],
                "max_tokens": 800,
                "temperature": 0.3,
                "top_p": 0.9
            })
            
            if response.status_code == 200:
                result = response.json()
                actual_type = result.get('type', 'unknown')
                
                # Check if classification is correct
                if actual_type == test_case['expected_type']:
                    print(f"✅ Classification: {actual_type} (CORRECT)")
                else:
                    print(f"❌ Classification: {actual_type} (Expected: {test_case['expected_type']})")
                
                print(f"📊 Tokens: {result.get('completion_tokens', 0)}")
                print(f"⏱️  Time: {result.get('query_time', 0):.2f}s")
                print(f"🌡️  Temperature: {result.get('temperature', 0)}")
                print(f"🎯 Top-p: {result.get('top_p', 0)}")
                
                # Show response preview
                response_text = result.get('response', '')
                preview = response_text[:200] + "..." if len(response_text) > 200 else response_text
                print(f"\n📝 Response Preview:")
                print(f"   {preview}")
                
                # Check if enhanced prompt was used
                if 'enhanced_prompt' in result:
                    print("✅ Enhanced prompt was used")
                else:
                    print("ℹ️  Standard prompt processing")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(response.text)
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 Classification Test Complete!")

if __name__ == "__main__":
    test_classification()
