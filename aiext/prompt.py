"""
Enhanced prompt engineering for StarCoder2 15B model
Optimized for code analysis and error detection tasks
"""

def classify_question(prompt: str, context: str = None) -> str:
    """Classify the type of question/task"""
    if "analyze" in prompt.lower() and ("bug" in prompt.lower() or "error" in prompt.lower()):
        return "error_analysis"
    elif "security" in prompt.lower() or "vulnerability" in prompt.lower():
        return "security_analysis"
    elif "performance" in prompt.lower() or "optimization" in prompt.lower():
        return "performance_analysis"
    else:
        return "general"

def build_error_analysis_prompt(code: str, language: str = "java") -> str:
    """Build optimized prompt for error analysis tasks"""
    
    prompt = f"""You are an expert {language.upper()} code analyzer specializing in finding critical bugs and security vulnerabilities.

**TASK**: Analyze the provided code and identify ALL critical issues. Do NOT provide fixed code - only analysis.

**REQUIRED OUTPUT FORMAT**:
## Critical Issues Found:

### Issue 1: [Issue Type]
- **Location**: [Specific line/method]
- **Problem**: [Detailed description]
- **Impact**: [Consequences if not fixed]
- **Root Cause**: [Why this happens]

### Issue 2: [Issue Type]
- **Location**: [Specific line/method]  
- **Problem**: [Detailed description]
- **Impact**: [Consequences if not fixed]
- **Root Cause**: [Why this happens]

[Continue for all issues found...]

## Summary:
- Total critical issues: [number]
- Most severe: [brief description]
- Recommended priority: [High/Medium/Low]

**ANALYSIS FOCUS**:
- Race conditions and thread safety
- Memory leaks and resource management  
- Security vulnerabilities
- Logic errors and edge cases
- Performance bottlenecks

**CODE TO ANALYZE**:
```{language}
{code}
```

**ANALYSIS**:"""

    return prompt

def build_concurrency_analysis_prompt(code: str) -> str:
    """Specialized prompt for concurrency bug analysis"""
    
    prompt = f"""You are a concurrency expert analyzing Java code for thread safety issues.

**SPECIFIC FOCUS**: Find ALL concurrency bugs, race conditions, deadlocks, and JMM violations.

**REQUIRED ANALYSIS**:

## Race Conditions:
[List each race condition with specific variables/fields involved]

## Deadlock Potential:
[Analyze lock ordering and potential deadlock scenarios]

## Memory Visibility Issues:
[Identify non-volatile fields accessed by multiple threads]

## JMM Violations:
[Java Memory Model violations and their consequences]

## Synchronization Problems:
[Incorrect or missing synchronization]

**CODE TO ANALYZE**:
```java
{code}
```

**DETAILED ANALYSIS**:"""

    return prompt

def build_prompt(prompt: str, context: str = None, task_type: str = "general") -> str:
    """Main prompt builder with task-specific optimization"""
    
    if task_type == "error_analysis":
        # Extract code from the original prompt
        if "```java" in prompt:
            start = prompt.find("```java") + 7
            end = prompt.find("```", start)
            if end != -1:
                code = prompt[start:end].strip()
                if "concurrency" in prompt.lower() or "thread" in prompt.lower():
                    return build_concurrency_analysis_prompt(code)
                else:
                    return build_error_analysis_prompt(code, "java")
    
    # For other task types, return enhanced version of original prompt
    enhanced_prompt = f"""**INSTRUCTION**: {prompt}

**IMPORTANT**: 
- Provide detailed, specific analysis
- Focus on critical issues only
- Use clear, structured format
- Explain the "why" behind each issue
- Do not provide code fixes unless explicitly requested

**RESPONSE**:"""
    
    return enhanced_prompt

def optimize_generation_params(task_type: str = "general") -> dict:
    """Return optimized generation parameters for different task types"""
    
    base_params = {
        "temperature": 0.3,
        "top_p": 0.9,
        "max_tokens": 1024,
        "repeat_penalty": 1.1
    }
    
    if task_type == "error_analysis":
        return {
            **base_params,
            "temperature": 0.4,  # Slightly higher for more detailed analysis
            "top_p": 0.85,       # More focused
            "max_tokens": 1500,  # More space for detailed analysis
        }
    elif task_type == "security_analysis":
        return {
            **base_params,
            "temperature": 0.2,  # Very focused for security
            "top_p": 0.8,
            "max_tokens": 2000,
        }
    
    return base_params
