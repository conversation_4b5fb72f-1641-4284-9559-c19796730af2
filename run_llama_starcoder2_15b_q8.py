import time
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
from contextlib import asynccontextmanager
from llama_cpp import <PERSON><PERSON><PERSON>

def classify_question(prompt: str) -> str:
    """Classify the type of question/task"""
    prompt_lower = prompt.lower()

    # Check for error analysis keywords
    if any(word in prompt_lower for word in ["analyze", "bug", "error", "issue", "problem", "vulnerability", "concurrency", "race condition", "deadlock"]):
        return "error_analysis"

    # Check for documentation keywords
    elif any(word in prompt_lower for word in ["document", "comment", "explain", "describe", "what does", "how does"]):
        return "code_documentation"

    # Check for code fix keywords
    elif any(word in prompt_lower for word in ["fix", "correct", "repair", "solve", "improve", "refactor", "optimize"]):
        return "code_fix"

    # Default to error analysis if contains code
    elif "```" in prompt:
        return "error_analysis"

    else:
        return "error_analysis"  # Default fallback

def build_enhanced_prompt(original_prompt: str, task_type: str = "error_analysis") -> str:
    """Build enhanced prompt using Alpaca format for StarCoder2"""

    # Extract code from the original prompt if present
    code = ""
    if "```java" in original_prompt:
        start = original_prompt.find("```java") + 7
        end = original_prompt.find("```", start)
        if end != -1:
            code = original_prompt[start:end].strip()
    elif "```" in original_prompt:
        start = original_prompt.find("```") + 3
        end = original_prompt.find("```", start)
        if end != -1:
            code = original_prompt[start:end].strip()

    if task_type == "error_analysis":
        if code:
            instruction = """You are an expert code analyzer. Analyze the provided Java code and identify ALL critical bugs, concurrency issues, race conditions, deadlock potential, and memory visibility problems.

IMPORTANT:
- Do NOT provide fixed code
- Focus ONLY on identifying and explaining problems
- Be specific about methods, variables, and line references
- Explain the consequences and root causes of each issue

Required output format:
## Critical Issues Found:

### Issue 1: [Type of Issue]
**Location**: [Specific method/variable]
**Problem**: [Detailed description]
**Consequence**: [What happens if not fixed]
**Root Cause**: [Why this occurs]

[Continue for all issues...]

## Summary:
- Total critical issues: [number]
- Most severe: [brief description]"""

            input_text = f"Java code to analyze:\n```java\n{code}\n```"

            enhanced_prompt = f"""### Instruction:
{instruction}

### Input:
{input_text}

### Response:
"""
        else:
            # Extract instruction from original prompt if it follows ### format
            if "### Instruction" in original_prompt:
                parts = original_prompt.split("### Response")
                instruction_part = parts[0].replace("### Instruction", "").strip()
                enhanced_prompt = f"""### Instruction:
{instruction_part}

Focus on detailed analysis of problems and issues. Use clear, structured format and explain reasoning.

### Response:
"""
            else:
                enhanced_prompt = f"""### Instruction:
{original_prompt}

Provide detailed analysis focusing on critical bugs and errors. Use clear, structured format.

### Response:
"""

    elif task_type == "code_documentation":
        if code:
            instruction = """You are an expert technical writer. Create clear, comprehensive documentation for the provided code.

Requirements:
- Explain what the code does in plain language
- Document all methods, classes, and important variables
- Include usage examples where appropriate
- Explain the purpose and design decisions

Output format:
## Code Overview:
[Brief description]

## Class/Method Documentation:
### [ClassName/MethodName]
**Purpose**: [What it does]
**Parameters**: [If applicable]
**Returns**: [If applicable]
**Usage**: [How to use it]

## Key Features:
- [Feature 1]
- [Feature 2]"""

            input_text = f"Code to document:\n```java\n{code}\n```"

            enhanced_prompt = f"""### Instruction:
{instruction}

### Input:
{input_text}

### Response:
"""
        else:
            enhanced_prompt = f"""### Instruction:
{original_prompt}

Provide clear, comprehensive documentation. Explain functionality in plain language and include examples where helpful.

### Response:
"""

    elif task_type == "code_fix":
        if code:
            instruction = """You are an expert software engineer. Identify problems in the provided code and provide corrected versions.

Requirements:
- First identify what's wrong
- Then provide the corrected code
- Explain what changes were made and why
- Ensure the fix addresses all issues

Output format:
## Issues Identified:
1. [Issue 1 description]
2. [Issue 2 description]

## Corrected Code:
```java
[Your corrected code here]
```

## Changes Made:
1. **[Change 1]**: [Explanation]
2. **[Change 2]**: [Explanation]"""

            input_text = f"Code to fix:\n```java\n{code}\n```"

            enhanced_prompt = f"""### Instruction:
{instruction}

### Input:
{input_text}

### Response:
"""
        else:
            enhanced_prompt = f"""### Instruction:
{original_prompt}

Identify problems clearly, provide corrected solutions, and explain all changes made.

### Response:
"""

    else:
        # Fallback for any other type - use Alpaca format
        enhanced_prompt = f"""### Instruction:
{original_prompt}

Provide detailed, specific response using clear, structured format.

### Response:
"""

    return enhanced_prompt

def get_optimized_params(task_type: str) -> dict:
    """Get optimized generation parameters based on task type"""

    if task_type == "error_analysis":
        return {
            "temperature": 0.4,      # Slightly higher for more detailed analysis
            "top_p": 0.85,          # More focused
            "max_tokens": 1500,     # More space for detailed analysis
            "repeat_penalty": 1.15   # Reduce repetition
        }
    elif task_type == "code_documentation":
        return {
            "temperature": 0.3,      # Balanced for clear documentation
            "top_p": 0.9,           # Allow some creativity in explanations
            "max_tokens": 2000,     # More space for comprehensive docs
            "repeat_penalty": 1.1    # Less strict on repetition for docs
        }
    elif task_type == "code_fix":
        return {
            "temperature": 0.2,      # Very focused for accurate fixes
            "top_p": 0.8,           # More deterministic
            "max_tokens": 1800,     # Space for code + explanations
            "repeat_penalty": 1.2    # Avoid repetitive fixes
        }
    else:
        return {
            "temperature": 0.3,
            "top_p": 0.9,
            "max_tokens": 1024,
            "repeat_penalty": 1.2
        }

# Request schema
class GenerationRequest(BaseModel):
    prompt: str
    context: Optional[str] = None
    max_tokens: Optional[int] = 1024
    temperature: Optional[float] = 0.3
    top_p: Optional[float] = 0.9
    stop_words: Optional[list[str]] = []

# Load model once on startup
llm = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global llm

    print("🚀 Loading model with llama.cpp...")
    try:
        llm = Llama(
            model_path="./models/starcoder2-15b-instruct-v0.1-Q8_0.gguf",
            n_ctx=8192,
            n_batch=192,
            use_mlock=False,
            n_gpu_layers=35,
            verbose=True
        )
        print("✅ Model loaded and ready for inference.")
    except Exception as e:
        print("❌ Failed to load model:", e)
        raise e

    yield


app = FastAPI(lifespan=lifespan)

@app.post("/generate")
def generate_text(req: GenerationRequest):
    if llm is None:
        raise HTTPException(status_code=500, detail="Model not loaded")

    # Classify the task type
    task_type = classify_question(req.prompt, req.context)

    # Build enhanced prompt
    enhanced_prompt = build_enhanced_prompt(req.prompt, task_type)

    # Get optimized parameters
    optimized_params = get_optimized_params(task_type)

    # Use optimized parameters, but allow request overrides
    final_params = {
        "max_tokens": req.max_tokens or optimized_params["max_tokens"],
        "temperature": req.temperature or optimized_params["temperature"],
        "top_p": req.top_p or optimized_params["top_p"],
        "repeat_penalty": optimized_params["repeat_penalty"]
    }

    print(f"\n=== TASK TYPE: {task_type} ===")
    print(f"Original prompt length: {len(req.prompt)}")
    print(f"Enhanced prompt length: {len(enhanced_prompt)}")
    print(f"Optimized params: {final_params}")
    print("=" * 50)

    try:
        start = time.time()
        output = llm.create_completion(
            prompt=enhanced_prompt,
            max_tokens=final_params["max_tokens"],
            temperature=final_params["temperature"],
            top_p=final_params["top_p"],
            repeat_penalty=final_params["repeat_penalty"],
            stop=req.stop_words,
        )
        end = time.time()
        response = output["choices"][0]["text"]
        completion_token = output["usage"]["completion_tokens"]
        query_time = end - start

        return {
            "type": task_type,
            "prompt": req.prompt,  # Return original prompt
            "enhanced_prompt": enhanced_prompt,  # Also return enhanced version
            "response": response,
            "max_tokens": final_params["max_tokens"],
            "top_p": final_params["top_p"],
            "temperature": final_params["temperature"],
            "completion_tokens": completion_token,
            "query_time": query_time,
            "tokens_per_second": completion_token / query_time if query_time > 0 else 0
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
