import time
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
from contextlib import asynccontextmanager
from llama_cpp import <PERSON><PERSON><PERSON>

def classify_question(prompt: str, context: str = None) -> str:
    """Classify the type of question/task"""
    prompt_lower = prompt.lower()

    # Check for error analysis keywords
    if any(word in prompt_lower for word in ["analyze", "bug", "error", "issue", "problem", "vulnerability", "concurrency", "race condition", "deadlock"]):
        return "error_analysis"

    # Check for documentation keywords
    elif any(word in prompt_lower for word in ["document", "comment", "explain", "describe", "what does", "how does"]):
        return "code_documentation"

    # Check for code fix keywords
    elif any(word in prompt_lower for word in ["fix", "correct", "repair", "solve", "improve", "refactor", "optimize"]):
        return "code_fix"

    # Default to error analysis if contains code
    elif "```" in prompt:
        return "error_analysis"

    else:
        return "error_analysis"  # Default fallback

def build_enhanced_prompt(original_prompt: str, task_type: str = "error_analysis") -> str:
    """Build enhanced prompt based on task type"""

    # Extract code from the original prompt if present
    code = ""
    if "```java" in original_prompt:
        start = original_prompt.find("```java") + 7
        end = original_prompt.find("```", start)
        if end != -1:
            code = original_prompt[start:end].strip()
    elif "```" in original_prompt:
        start = original_prompt.find("```") + 3
        end = original_prompt.find("```", start)
        if end != -1:
            code = original_prompt[start:end].strip()

    if task_type == "error_analysis":
        if code:
            enhanced_prompt = f"""You are an expert code analyzer. Your task is to find and explain ALL critical bugs and issues in the provided code.

**IMPORTANT INSTRUCTIONS**:
- Do NOT provide fixed code
- Focus ONLY on identifying and explaining problems
- Be specific about methods, variables, and line references
- Explain the consequences of each issue

**REQUIRED OUTPUT FORMAT**:

## Critical Issues Found:

### Issue 1: [Type of Issue]
**Location**: [Specific method/variable]
**Problem**: [Detailed description of what's wrong]
**Consequence**: [What happens if not fixed]
**Root Cause**: [Why this problem occurs]

### Issue 2: [Type of Issue]
**Location**: [Specific method/variable]
**Problem**: [Detailed description of what's wrong]
**Consequence**: [What happens if not fixed]
**Root Cause**: [Why this problem occurs]

[Continue for all issues...]

## Summary:
- Total critical issues found: [number]
- Most severe issue: [brief description]

**CODE TO ANALYZE**:
```java
{code}
```

**ANALYSIS**:"""
        else:
            enhanced_prompt = f"""{original_prompt}

**IMPORTANT**:
- Provide detailed analysis of problems and issues
- Focus on critical bugs and errors
- Use clear, structured format
- Explain the reasoning behind each finding"""

    elif task_type == "code_documentation":
        if code:
            enhanced_prompt = f"""You are an expert technical writer. Your task is to create clear, comprehensive documentation for the provided code.

**IMPORTANT INSTRUCTIONS**:
- Explain what the code does in plain language
- Document all methods, classes, and important variables
- Include usage examples where appropriate
- Explain the purpose and design decisions

**REQUIRED OUTPUT FORMAT**:

## Code Overview:
[Brief description of what this code does]

## Class/Method Documentation:

### [ClassName/MethodName]
**Purpose**: [What it does]
**Parameters**: [If applicable]
**Returns**: [If applicable]
**Usage**: [How to use it]

[Continue for all components...]

## Key Features:
- [Feature 1]
- [Feature 2]
- [Feature 3]

**CODE TO DOCUMENT**:
```java
{code}
```

**DOCUMENTATION**:"""
        else:
            enhanced_prompt = f"""{original_prompt}

**IMPORTANT**:
- Provide clear, comprehensive documentation
- Explain functionality in plain language
- Include examples where helpful
- Focus on usability and understanding"""

    elif task_type == "code_fix":
        if code:
            enhanced_prompt = f"""You are an expert software engineer. Your task is to identify problems in the code and provide corrected versions.

**IMPORTANT INSTRUCTIONS**:
- First identify what's wrong
- Then provide the corrected code
- Explain what changes were made and why
- Ensure the fix addresses all issues

**REQUIRED OUTPUT FORMAT**:

## Issues Identified:
1. [Issue 1 description]
2. [Issue 2 description]
[Continue for all issues...]

## Corrected Code:
```java
[Your corrected code here]
```

## Changes Made:
1. **[Change 1]**: [Explanation of why this change was needed]
2. **[Change 2]**: [Explanation of why this change was needed]
[Continue for all changes...]

**ORIGINAL CODE**:
```java
{code}
```

**ANALYSIS AND FIX**:"""
        else:
            enhanced_prompt = f"""{original_prompt}

**IMPORTANT**:
- Identify problems clearly
- Provide corrected solutions
- Explain all changes made
- Ensure fixes are complete and correct"""

    else:
        # Fallback for any other type
        enhanced_prompt = f"""{original_prompt}

**IMPORTANT**:
- Provide detailed, specific response
- Use clear, structured format
- Be thorough and precise"""

    return enhanced_prompt

def get_optimized_params(task_type: str) -> dict:
    """Get optimized generation parameters based on task type"""

    if task_type == "error_analysis":
        return {
            "temperature": 0.4,      # Slightly higher for more detailed analysis
            "top_p": 0.85,          # More focused
            "max_tokens": 1500,     # More space for detailed analysis
            "repeat_penalty": 1.15   # Reduce repetition
        }
    elif task_type == "security_analysis":
        return {
            "temperature": 0.2,      # Very focused for security
            "top_p": 0.8,
            "max_tokens": 2000,
            "repeat_penalty": 1.1
        }
    else:
        return {
            "temperature": 0.3,
            "top_p": 0.9,
            "max_tokens": 1024,
            "repeat_penalty": 1.2
        }

# Request schema
class GenerationRequest(BaseModel):
    prompt: str
    context: Optional[str] = None
    max_tokens: Optional[int] = 1024
    temperature: Optional[float] = 0.3
    top_p: Optional[float] = 0.9
    stop_words: Optional[list[str]] = []

# Load model once on startup
llm = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global llm

    print("🚀 Loading model with llama.cpp...")
    try:
        llm = Llama(
            model_path="./models/starcoder2-15b-instruct-v0.1-Q8_0.gguf",
            n_ctx=8192,
            n_batch=192,
            use_mlock=False,
            n_gpu_layers=35,
            verbose=True
        )
        print("✅ Model loaded and ready for inference.")
    except Exception as e:
        print("❌ Failed to load model:", e)
        raise e

    yield


app = FastAPI(lifespan=lifespan)

@app.post("/generate")
def generate_text(req: GenerationRequest):
    if llm is None:
        raise HTTPException(status_code=500, detail="Model not loaded")

    # Classify the task type
    task_type = classify_question(req.prompt, req.context)

    # Build enhanced prompt
    enhanced_prompt = build_enhanced_prompt(req.prompt, task_type)

    # Get optimized parameters
    optimized_params = get_optimized_params(task_type)

    # Use optimized parameters, but allow request overrides
    final_params = {
        "max_tokens": req.max_tokens or optimized_params["max_tokens"],
        "temperature": req.temperature or optimized_params["temperature"],
        "top_p": req.top_p or optimized_params["top_p"],
        "repeat_penalty": optimized_params["repeat_penalty"]
    }

    print(f"\n=== TASK TYPE: {task_type} ===")
    print(f"Original prompt length: {len(req.prompt)}")
    print(f"Enhanced prompt length: {len(enhanced_prompt)}")
    print(f"Optimized params: {final_params}")
    print("=" * 50)

    try:
        start = time.time()
        output = llm.create_completion(
            prompt=enhanced_prompt,
            max_tokens=final_params["max_tokens"],
            temperature=final_params["temperature"],
            top_p=final_params["top_p"],
            repeat_penalty=final_params["repeat_penalty"],
            stop=req.stop_words,
        )
        end = time.time()
        response = output["choices"][0]["text"]
        completion_token = output["usage"]["completion_tokens"]
        query_time = end - start

        return {
            "type": task_type,
            "prompt": req.prompt,  # Return original prompt
            "enhanced_prompt": enhanced_prompt,  # Also return enhanced version
            "response": response,
            "max_tokens": final_params["max_tokens"],
            "top_p": final_params["top_p"],
            "temperature": final_params["temperature"],
            "completion_tokens": completion_token,
            "query_time": query_time,
            "tokens_per_second": completion_token / query_time if query_time > 0 else 0
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
