import time
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Optional
from contextlib import asynccontextmanager
from llama_cpp import <PERSON><PERSON><PERSON>
from aiext.prompt import build_prompt, classify_question

# Request schema
class GenerationRequest(BaseModel):
    prompt: str
    context: Optional[str] = None
    max_tokens: Optional[int] = 1024
    temperature: Optional[float] = 0.3
    top_p: Optional[float] = 0.9
    stop_words: Optional[list[str]] = []

# Load model once on startup
llm = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global llm

    print("🚀 Loading model with llama.cpp...")
    try:
        llm = Llama(
            model_path="./models/starcoder2-15b-instruct-v0.1-Q8_0.gguf",
            n_ctx=8192,
            n_batch=192,
            use_mlock=False,
            n_gpu_layers=35,
            verbose=True
        )
        print("✅ Model loaded and ready for inference.")
    except Exception as e:
        print("❌ Failed to load model:", e)
        raise e

    yield


app = FastAPI(lifespan=lifespan)

@app.post("/generate")
def generate_text(req: GenerationRequest):
    if llm is None:
        raise HTTPException(status_code=500, detail="Model not loaded")

    prompt = req.prompt
    print(f"\nprompt === {prompt}\n")
    print("prompt === end")

    print(f"{req}")

    try:
        start = time.time()
        output = llm.create_completion(
            prompt=prompt,
            max_tokens=req.max_tokens,
            temperature=req.temperature,
            top_p=req.top_p,
            repeat_penalty=1.2,
            stop=req.stop_words,
        )
        end = time.time()
        response = output["choices"][0]["text"]
        completion_token = output["usage"]["completion_tokens"]
        query_time = end - start

        return {
            "type": classify_question(req.prompt, req.context),
            "prompt": prompt,
            "response": response,
            "max_tokens": req.max_tokens,
            "top_p": req.top_p,
            "temperature": req.temperature,
            "completion_tokens": completion_token,
            "query_time": query_time,
            "tokens_per_second": completion_token / query_time
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
