import time
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
from contextlib import asynccontextmanager
from llama_cpp import <PERSON><PERSON><PERSON>

def classify_question(prompt: str, context: str = None) -> str:
    """Classify the type of question/task"""
    if "analyze" in prompt.lower() and ("bug" in prompt.lower() or "error" in prompt.lower() or "concurrency" in prompt.lower()):
        return "error_analysis"
    elif "security" in prompt.lower() or "vulnerability" in prompt.lower():
        return "security_analysis"
    elif "performance" in prompt.lower() or "optimization" in prompt.lower():
        return "performance_analysis"
    else:
        return "general"

def build_enhanced_prompt(original_prompt: str, task_type: str = "general") -> str:
    """Build enhanced prompt for better analysis results"""

    if task_type == "error_analysis":
        # Extract code from the original prompt if present
        if "```java" in original_prompt:
            start = original_prompt.find("```java") + 7
            end = original_prompt.find("```", start)
            if end != -1:
                code = original_prompt[start:end].strip()

                enhanced_prompt = f"""You are an expert Java code analyzer. Your task is to find and explain ALL critical bugs and issues in the provided code.

**IMPORTANT INSTRUCTIONS**:
- Do NOT provide fixed code
- Focus ONLY on identifying and explaining problems
- Be specific about line numbers and variable names
- Explain the consequences of each issue

**REQUIRED OUTPUT FORMAT**:

## Critical Issues Found:

### Issue 1: [Type of Issue]
**Location**: [Specific method/line]
**Problem**: [Detailed description of what's wrong]
**Consequence**: [What happens if not fixed]
**Root Cause**: [Why this problem occurs]

### Issue 2: [Type of Issue]
**Location**: [Specific method/line]
**Problem**: [Detailed description of what's wrong]
**Consequence**: [What happens if not fixed]
**Root Cause**: [Why this problem occurs]

[Continue for all issues...]

## Summary:
- Total critical issues found: [number]
- Most severe issue: [brief description]

**CODE TO ANALYZE**:
```java
{code}
```

**ANALYSIS**:"""
                return enhanced_prompt

    # For non-error analysis or if no code found, enhance the original prompt
    enhanced_prompt = f"""{original_prompt}

**IMPORTANT**:
- Provide detailed, specific analysis
- Focus on critical issues only
- Use clear, structured format
- Explain the reasoning behind each finding
- Be thorough and precise"""

    return enhanced_prompt

def get_optimized_params(task_type: str) -> dict:
    """Get optimized generation parameters based on task type"""

    if task_type == "error_analysis":
        return {
            "temperature": 0.4,      # Slightly higher for more detailed analysis
            "top_p": 0.85,          # More focused
            "max_tokens": 1500,     # More space for detailed analysis
            "repeat_penalty": 1.15   # Reduce repetition
        }
    elif task_type == "security_analysis":
        return {
            "temperature": 0.2,      # Very focused for security
            "top_p": 0.8,
            "max_tokens": 2000,
            "repeat_penalty": 1.1
        }
    else:
        return {
            "temperature": 0.3,
            "top_p": 0.9,
            "max_tokens": 1024,
            "repeat_penalty": 1.2
        }

# Request schema
class GenerationRequest(BaseModel):
    prompt: str
    context: Optional[str] = None
    max_tokens: Optional[int] = 1024
    temperature: Optional[float] = 0.3
    top_p: Optional[float] = 0.9
    stop_words: Optional[list[str]] = []

# Load model once on startup
llm = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    global llm

    print("🚀 Loading model with llama.cpp...")
    try:
        llm = Llama(
            model_path="./models/starcoder2-15b-instruct-v0.1-Q8_0.gguf",
            n_ctx=8192,
            n_batch=192,
            use_mlock=False,
            n_gpu_layers=35,
            verbose=True
        )
        print("✅ Model loaded and ready for inference.")
    except Exception as e:
        print("❌ Failed to load model:", e)
        raise e

    yield


app = FastAPI(lifespan=lifespan)

@app.post("/generate")
def generate_text(req: GenerationRequest):
    if llm is None:
        raise HTTPException(status_code=500, detail="Model not loaded")

    # Classify the task type
    task_type = classify_question(req.prompt, req.context)

    # Build enhanced prompt
    enhanced_prompt = build_enhanced_prompt(req.prompt, task_type)

    # Get optimized parameters
    optimized_params = get_optimized_params(task_type)

    # Use optimized parameters, but allow request overrides
    final_params = {
        "max_tokens": req.max_tokens or optimized_params["max_tokens"],
        "temperature": req.temperature or optimized_params["temperature"],
        "top_p": req.top_p or optimized_params["top_p"],
        "repeat_penalty": optimized_params["repeat_penalty"]
    }

    print(f"\n=== TASK TYPE: {task_type} ===")
    print(f"Original prompt length: {len(req.prompt)}")
    print(f"Enhanced prompt length: {len(enhanced_prompt)}")
    print(f"Optimized params: {final_params}")
    print("=" * 50)

    try:
        start = time.time()
        output = llm.create_completion(
            prompt=enhanced_prompt,
            max_tokens=final_params["max_tokens"],
            temperature=final_params["temperature"],
            top_p=final_params["top_p"],
            repeat_penalty=final_params["repeat_penalty"],
            stop=req.stop_words,
        )
        end = time.time()
        response = output["choices"][0]["text"]
        completion_token = output["usage"]["completion_tokens"]
        query_time = end - start

        return {
            "type": task_type,
            "prompt": req.prompt,  # Return original prompt
            "enhanced_prompt": enhanced_prompt,  # Also return enhanced version
            "response": response,
            "max_tokens": final_params["max_tokens"],
            "top_p": final_params["top_p"],
            "temperature": final_params["temperature"],
            "completion_tokens": completion_token,
            "query_time": query_time,
            "tokens_per_second": completion_token / query_time if query_time > 0 else 0
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
